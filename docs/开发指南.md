# Trade Manage Frontend 开发指南

## 项目概述

Trade Manage Frontend 是一个基于 Next.js 15 + TypeScript + Shadcn/ui 构建的现代化管理后台模板。项目采用 **Colocation 架构**，按功能模块组织代码，提供了灵活的布局系统、强大的数据表格、多样化的仪表板和完整的认证框架。

### 核心特性
- ✅ Next.js 15 App Router 架构
- ✅ TypeScript 全栈类型安全
- ✅ Shadcn/ui 组件库
- ✅ TanStack Table 数据表格
- ✅ Zustand 状态管理
- ✅ 多主题支持 (明暗模式 + 颜色主题)
- ✅ 响应式布局系统
- ✅ 认证中间件框架

## 1. 如何添加菜单

### 菜单配置文件位置
主菜单配置文件位于 [`src/navigation/sidebar/sidebar-items.ts`](src/navigation/sidebar/sidebar-items.ts)

### 菜单数据结构
```typescript
export interface NavSubItem {
  title: string;        // 菜单名称
  url: string;         // 路由地址
  icon?: LucideIcon;   // 图标（可选）
  comingSoon?: boolean; // 是否显示"即将推出"
  newTab?: boolean;    // 是否在新标签页打开
  isNew?: boolean;     // 是否显示"新"标记
}

export interface NavMainItem {
  title: string;
  url: string;
  icon?: LucideIcon;
  subItems?: NavSubItem[]; // 子菜单
  comingSoon?: boolean;
  newTab?: boolean;
  isNew?: boolean;
}

export interface NavGroup {
  id: number;
  label?: string;      // 分组标题
  items: NavMainItem[];
}
```

### 添加菜单步骤

#### 步骤 1: 添加图标引用
```typescript
// 在文件顶部导入需要的图标
import {
  Users,      // 用户管理图标
  Settings,   // 设置图标
  Package,    // 产品图标
  // ... 其他图标
} from "lucide-react";
```

#### 步骤 2: 添加菜单项
```typescript
export const sidebarItems: NavGroup[] = [
  {
    id: 1,
    label: "主要功能",
    items: [
      // 添加新的主菜单
      {
        title: "产品管理",
        url: "/dashboard/products",
        icon: Package,
      },
      // 添加带子菜单的菜单项
      {
        title: "用户管理",
        url: "/dashboard/users",
        icon: Users,
        subItems: [
          { title: "用户列表", url: "/dashboard/users/list" },
          { title: "角色管理", url: "/dashboard/users/roles" },
          { title: "权限设置", url: "/dashboard/users/permissions" }
        ]
      }
    ]
  }
];
```

#### 步骤 3: 创建对应页面
根据菜单路由创建对应的页面文件：
```
src/app/(main)/dashboard/products/
├── page.tsx                 # 主页面
├── _components/            # 页面专用组件
│   ├── product-list.tsx
│   ├── product-form.tsx
│   └── columns.tsx
└── [id]/                   # 动态路由
    └── page.tsx
```

#### 步骤 4: 菜单权限控制（可选）
```typescript
// 根据用户角色动态显示菜单
const getMenuItems = (userRole: string) => {
  return sidebarItems.map(group => ({
    ...group,
    items: group.items.filter(item => {
      // 根据角色过滤菜单项
      if (item.url.includes('/admin') && userRole !== 'admin') {
        return false;
      }
      return true;
    })
  }));
};
```

## 2. 认证系统

### 认证中间件系统
认证中间件位于 [`src/middleware/auth-middleware.ts`](src/middleware/auth-middleware.ts)，目前处于禁用状态。

#### 当前认证逻辑
```typescript
export function authMiddleware(req: NextRequest) {
  const { pathname } = req.nextUrl;
  const isLoggedIn = req.cookies.get("auth-token");

  // 保护 /dashboard 路径
  if (!isLoggedIn && pathname.startsWith("/dashboard")) {
    return NextResponse.redirect(new URL("/auth/login", req.url));
  }

  // 已登录用户访问登录页时重定向到仪表板
  if (isLoggedIn && pathname === "/auth/login") {
    return NextResponse.redirect(new URL("/dashboard", req.url));
  }

  return NextResponse.next();
}
```

#### 启用认证系统
要启用认证系统，需要：

1. **重命名中间件文件**：
   ```bash
   mv src/middleware.disabled.ts src/middleware.ts
   ```

2. **配置认证令牌**：
   ```typescript
   // src/lib/auth.ts
   export const setAuthToken = (token: string) => {
     document.cookie = `auth-token=${token}; path=/; secure; samesite=strict`;
   };

   export const removeAuthToken = () => {
     document.cookie = 'auth-token=; path=/; expires=Thu, 01 Jan 1970 00:00:01 GMT;';
   };

   export const getAuthToken = () => {
     if (typeof window === 'undefined') return null;
     return document.cookie
       .split('; ')
       .find(row => row.startsWith('auth-token='))
       ?.split('=')[1];
   };
   ```

3. **集成登录表单**：
   ```typescript
   // 在登录成功后设置令牌
   const handleLogin = async (credentials: LoginCredentials) => {
     try {
       const response = await fetch('/api/auth/login', {
         method: 'POST',
         headers: { 'Content-Type': 'application/json' },
         body: JSON.stringify(credentials)
       });
       
       const { token } = await response.json();
       setAuthToken(token);
       
       // 重定向到仪表板
       router.push('/dashboard');
     } catch (error) {
       console.error('Login failed:', error);
     }
   };
   ```

#### 基于会话的认证（推荐）
```typescript
// src/lib/session.ts
import { cookies } from 'next/headers';
import { encrypt, decrypt } from '@/lib/crypto';

export async function createSession(userId: string) {
  const expiresAt = new Date(Date.now() + 7 * 24 * 60 * 60 * 1000); // 7天
  
  const session = await encrypt({ userId, expiresAt });
  
  const cookieStore = await cookies();
  cookieStore.set('session', session, {
    httpOnly: true,
    secure: true,
    expires: expiresAt,
    sameSite: 'lax',
    path: '/',
  });
}

export async function verifySession() {
  const cookieStore = await cookies();
  const cookie = cookieStore.get('session')?.value;
  
  if (!cookie) return null;
  
  const session = await decrypt(cookie);
  
  if (!session?.userId) return null;
  
  return { userId: session.userId };
}

export async function deleteSession() {
  const cookieStore = await cookies();
  cookieStore.delete('session');
}
```

## 3. 常规分页页面开发

### 页面结构（Colocation 模式）
```
src/app/(main)/dashboard/[feature]/
├── page.tsx                    # 主页面组件
├── loading.tsx                 # 加载状态页面
├── error.tsx                   # 错误状态页面
├── _components/               # 功能专用组件
│   ├── [feature]-list.tsx     # 列表组件
│   ├── [feature]-form.tsx     # 表单组件
│   ├── columns.tsx            # 表格列定义
│   ├── schema.ts              # 数据模型
│   └── filters.tsx            # 筛选组件
└── [id]/                      # 详情页动态路由
    ├── page.tsx
    ├── edit/
    │   └── page.tsx
    └── _components/
```

### 基础分页页面模板

#### 1. 数据模型定义
```typescript
// _components/schema.ts
import { z } from 'zod';

export const productSchema = z.object({
  id: z.string(),
  name: z.string(),
  category: z.string(),
  price: z.number(),
  stock: z.number(),
  status: z.enum(['active', 'inactive']),
  createdAt: z.string(),
  updatedAt: z.string(),
});

export type Product = z.infer<typeof productSchema>;
```

#### 2. 表格列定义
```typescript
// _components/columns.tsx
import { ColumnDef } from "@tanstack/react-table";
import { DataTableColumnHeader } from "@/components/data-table/data-table-column-header";
import { Badge } from "@/components/ui/badge";
import { Product } from "./schema";

export const productColumns: ColumnDef<Product>[] = [
  {
    id: "select",
    header: ({ table }) => (
      <Checkbox
        checked={table.getIsAllPageRowsSelected()}
        onCheckedChange={(value) => table.toggleAllPageRowsSelected(!!value)}
      />
    ),
    cell: ({ row }) => (
      <Checkbox
        checked={row.getIsSelected()}
        onCheckedChange={(value) => row.toggleSelected(!!value)}
      />
    ),
    enableSorting: false,
    enableHiding: false,
  },
  {
    accessorKey: "name",
    header: ({ column }) => <DataTableColumnHeader column={column} title="产品名称" />,
    cell: ({ row }) => <span className="font-medium">{row.original.name}</span>,
  },
  {
    accessorKey: "category",
    header: ({ column }) => <DataTableColumnHeader column={column} title="分类" />,
  },
  {
    accessorKey: "price",
    header: ({ column }) => <DataTableColumnHeader column={column} title="价格" />,
    cell: ({ row }) => {
      const price = parseFloat(row.getValue("price"));
      return <span className="font-mono">¥{price.toFixed(2)}</span>;
    },
  },
  {
    accessorKey: "status",
    header: ({ column }) => <DataTableColumnHeader column={column} title="状态" />,
    cell: ({ row }) => {
      const status = row.getValue("status") as string;
      return (
        <Badge variant={status === 'active' ? 'default' : 'secondary'}>
          {status === 'active' ? '启用' : '禁用'}
        </Badge>
      );
    },
  },
  {
    id: "actions",
    cell: ({ row }) => <ProductActions product={row.original} />,
    enableSorting: false,
  },
];
```

#### 3. 主页面组件
```typescript
// page.tsx
'use client';

import { useState, useEffect } from 'react';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { DataTable } from '@/components/data-table/data-table';
import { DataTablePagination } from '@/components/data-table/data-table-pagination';
import { useDataTableInstance } from '@/hooks/use-data-table-instance';
import { productColumns } from './_components/columns';
import { ProductFilters } from './_components/filters';
import { type Product } from './_components/schema';

interface ProductPageProps {
  searchParams: {
    page?: string;
    pageSize?: string;
    search?: string;
    category?: string;
    status?: string;
  };
}

export default function ProductPage({ searchParams }: ProductPageProps) {
  const [data, setData] = useState<Product[]>([]);
  const [loading, setLoading] = useState(true);
  const [totalCount, setTotalCount] = useState(0);

  // 解析查询参数
  const page = Number(searchParams.page) || 1;
  const pageSize = Number(searchParams.pageSize) || 10;
  const search = searchParams.search || '';
  const category = searchParams.category || '';
  const status = searchParams.status || '';

  // 初始化数据表格
  const table = useDataTableInstance({
    data,
    columns: productColumns,
    defaultPageIndex: page - 1,
    defaultPageSize: pageSize,
  });

  // 获取数据
  useEffect(() => {
    const fetchData = async () => {
      setLoading(true);
      try {
        const params = new URLSearchParams({
          page: page.toString(),
          pageSize: pageSize.toString(),
          ...(search && { search }),
          ...(category && { category }),
          ...(status && { status }),
        });

        const response = await fetch(`/api/products?${params}`);
        const result = await response.json();
        
        setData(result.data);
        setTotalCount(result.total);
      } catch (error) {
        console.error('Failed to fetch products:', error);
      } finally {
        setLoading(false);
      }
    };

    fetchData();
  }, [page, pageSize, search, category, status]);

  if (loading) {
    return <div>加载中...</div>;
  }

  return (
    <div className="space-y-6">
      <div className="flex items-center justify-between">
        <h1 className="text-2xl font-bold">产品管理</h1>
        <Button>
          <Plus className="mr-2 h-4 w-4" />
          添加产品
        </Button>
      </div>

      <Card>
        <CardHeader>
          <CardTitle>产品列表</CardTitle>
        </CardHeader>
        <CardContent>
          <div className="space-y-4">
            <ProductFilters />
            <DataTable 
              table={table} 
              columns={productColumns}
            />
            <DataTablePagination 
              table={table}
              totalCount={totalCount}
            />
          </div>
        </CardContent>
      </Card>
    </div>
  );
}
```

## 4. 查询条件添加

### 筛选组件设计
```typescript
// _components/filters.tsx
'use client';

import { useRouter, useSearchParams } from 'next/navigation';
import { useState, useCallback } from 'react';
import { Input } from '@/components/ui/input';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { Button } from '@/components/ui/button';
import { Search, X } from 'lucide-react';

export function ProductFilters() {
  const router = useRouter();
  const searchParams = useSearchParams();
  
  const [filters, setFilters] = useState({
    search: searchParams.get('search') || '',
    category: searchParams.get('category') || '',
    status: searchParams.get('status') || '',
  });

  // 更新URL参数
  const updateFilters = useCallback((newFilters: typeof filters) => {
    const params = new URLSearchParams(searchParams);
    
    // 重置页码
    params.delete('page');
    
    // 更新筛选参数
    Object.entries(newFilters).forEach(([key, value]) => {
      if (value) {
        params.set(key, value);
      } else {
        params.delete(key);
      }
    });

    router.push(`?${params.toString()}`);
  }, [router, searchParams]);

  const handleSearch = () => {
    updateFilters(filters);
  };

  const handleReset = () => {
    const resetFilters = { search: '', category: '', status: '' };
    setFilters(resetFilters);
    updateFilters(resetFilters);
  };

  const handleKeyDown = (e: React.KeyboardEvent) => {
    if (e.key === 'Enter') {
      handleSearch();
    }
  };

  return (
    <div className="flex flex-wrap gap-4 p-4 bg-gray-50 rounded-lg">
      <div className="flex-1 min-w-64">
        <Input
          placeholder="搜索产品名称..."
          value={filters.search}
          onChange={(e) => setFilters(prev => ({ ...prev, search: e.target.value }))}
          onKeyDown={handleKeyDown}
          className="w-full"
        />
      </div>
      
      <Select
        value={filters.category}
        onValueChange={(value) => setFilters(prev => ({ ...prev, category: value }))}
      >
        <SelectTrigger className="w-48">
          <SelectValue placeholder="选择分类" />
        </SelectTrigger>
        <SelectContent>
          <SelectItem value="">全部分类</SelectItem>
          <SelectItem value="electronics">电子产品</SelectItem>
          <SelectItem value="clothing">服装</SelectItem>
          <SelectItem value="books">图书</SelectItem>
        </SelectContent>
      </Select>

      <Select
        value={filters.status}
        onValueChange={(value) => setFilters(prev => ({ ...prev, status: value }))}
      >
        <SelectTrigger className="w-32">
          <SelectValue placeholder="状态" />
        </SelectTrigger>
        <SelectContent>
          <SelectItem value="">全部</SelectItem>
          <SelectItem value="active">启用</SelectItem>
          <SelectItem value="inactive">禁用</SelectItem>
        </SelectContent>
      </Select>

      <div className="flex gap-2">
        <Button onClick={handleSearch}>
          <Search className="mr-2 h-4 w-4" />
          搜索
        </Button>
        <Button variant="outline" onClick={handleReset}>
          <X className="mr-2 h-4 w-4" />
          重置
        </Button>
      </div>
    </div>
  );
}
```

### 高级筛选组件
```typescript
// _components/advanced-filters.tsx
'use client';

import { useState } from 'react';
import { Calendar } from '@/components/ui/calendar';
import { Popover, PopoverContent, PopoverTrigger } from '@/components/ui/popover';
import { Button } from '@/components/ui/button';
import { CalendarIcon, Filter } from 'lucide-react';
import { format } from 'date-fns';
import { zhCN } from 'date-fns/locale';

interface DateRangeFilter {
  from: Date | undefined;
  to: Date | undefined;
}

export function AdvancedFilters() {
  const [dateRange, setDateRange] = useState<DateRangeFilter>({
    from: undefined,
    to: undefined,
  });
  const [priceRange, setPriceRange] = useState({
    min: '',
    max: '',
  });

  return (
    <div className="grid grid-cols-1 md:grid-cols-3 gap-4 p-4 border rounded-lg">
      {/* 日期范围筛选 */}
      <div className="space-y-2">
        <label className="text-sm font-medium">创建日期</label>
        <Popover>
          <PopoverTrigger asChild>
            <Button variant="outline" className="w-full justify-start text-left">
              <CalendarIcon className="mr-2 h-4 w-4" />
              {dateRange.from ? (
                dateRange.to ? (
                  <>
                    {format(dateRange.from, "yyyy-MM-dd", { locale: zhCN })} -{" "}
                    {format(dateRange.to, "yyyy-MM-dd", { locale: zhCN })}
                  </>
                ) : (
                  format(dateRange.from, "yyyy-MM-dd", { locale: zhCN })
                )
              ) : (
                "选择日期范围"
              )}
            </Button>
          </PopoverTrigger>
          <PopoverContent className="w-auto p-0" align="start">
            <Calendar
              mode="range"
              selected={{ from: dateRange.from, to: dateRange.to }}
              onSelect={(range) => setDateRange({ from: range?.from, to: range?.to })}
              numberOfMonths={2}
            />
          </PopoverContent>
        </Popover>
      </div>

      {/* 价格范围筛选 */}
      <div className="space-y-2">
        <label className="text-sm font-medium">价格范围</label>
        <div className="flex gap-2">
          <Input
            placeholder="最低价"
            type="number"
            value={priceRange.min}
            onChange={(e) => setPriceRange(prev => ({ ...prev, min: e.target.value }))}
          />
          <span className="flex items-center text-gray-500">-</span>
          <Input
            placeholder="最高价"
            type="number"
            value={priceRange.max}
            onChange={(e) => setPriceRange(prev => ({ ...prev, max: e.target.value }))}
          />
        </div>
      </div>

      {/* 应用筛选按钮 */}
      <div className="flex items-end">
        <Button className="w-full">
          <Filter className="mr-2 h-4 w-4" />
          应用筛选
        </Button>
      </div>
    </div>
  );
}
```

## 5. AWS Lambda 请求转发设计

### API 路由结构
```
src/app/api/
├── auth/
│   ├── login/route.ts
│   └── logout/route.ts
├── products/
│   ├── route.ts              # GET /api/products, POST /api/products
│   └── [id]/route.ts         # GET/PUT/DELETE /api/products/[id]
├── users/
│   └── route.ts
└── lambda-proxy/
    └── [...path]/route.ts    # 通用 Lambda 代理
```

### Lambda 代理配置
```typescript
// src/lib/lambda-config.ts
export const lambdaConfig = {
  baseUrl: process.env.AWS_LAMBDA_BASE_URL || 'https://your-lambda-api.amazonaws.com',
  region: process.env.AWS_REGION || 'us-east-1',
  apiKey: process.env.AWS_API_KEY,
  timeout: 30000, // 30秒超时
};

// API 路径映射
export const apiPathMapping = {
  '/api/products': '/prod/products',
  '/api/users': '/prod/users',
  '/api/orders': '/prod/orders',
  // 添加更多映射...
};
```

### 通用 Lambda 代理
```typescript
// src/app/api/lambda-proxy/[...path]/route.ts
import { NextRequest, NextResponse } from 'next/server';
import { lambdaConfig, apiPathMapping } from '@/lib/lambda-config';

async function forwardToLambda(
  request: NextRequest,
  method: string,
  path: string[]
) {
  try {
    // 构建 Lambda API 路径
    const apiPath = `/api/${path.join('/')}`;
    const lambdaPath = apiPathMapping[apiPath] || apiPath;
    const lambdaUrl = `${lambdaConfig.baseUrl}${lambdaPath}`;

    // 转发请求头（排除一些 Next.js 特有的头）
    const headers = new Headers();
    request.headers.forEach((value, key) => {
      if (!key.startsWith('x-') && key !== 'host') {
        headers.set(key, value);
      }
    });

    // 添加 API 密钥
    if (lambdaConfig.apiKey) {
      headers.set('x-api-key', lambdaConfig.apiKey);
    }

    // 添加用户认证信息
    const authToken = request.cookies.get('auth-token')?.value;
    if (authToken) {
      headers.set('Authorization', `Bearer ${authToken}`);
    }

    // 准备请求体
    let body = undefined;
    if (['POST', 'PUT', 'PATCH'].includes(method)) {
      body = await request.text();
    }

    // 转发请求到 Lambda
    const lambdaResponse = await fetch(lambdaUrl, {
      method,
      headers,
      body,
      signal: AbortSignal.timeout(lambdaConfig.timeout),
    });

    // 转发响应
    const responseBody = await lambdaResponse.text();
    
    return new NextResponse(responseBody, {
      status: lambdaResponse.status,
      statusText: lambdaResponse.statusText,
      headers: {
        'Content-Type': lambdaResponse.headers.get('Content-Type') || 'application/json',
        // 转发其他必要的响应头
        ...(lambdaResponse.headers.get('Cache-Control') && {
          'Cache-Control': lambdaResponse.headers.get('Cache-Control')!
        }),
      },
    });

  } catch (error) {
    console.error('Lambda proxy error:', error);
    
    if (error instanceof Error && error.name === 'TimeoutError') {
      return NextResponse.json(
        { error: 'Request timeout' },
        { status: 504 }
      );
    }

    return NextResponse.json(
      { error: 'Internal server error' },
      { status: 500 }
    );
  }
}

export async function GET(
  request: NextRequest,
  { params }: { params: { path: string[] } }
) {
  return forwardToLambda(request, 'GET', params.path);
}

export async function POST(
  request: NextRequest,
  { params }: { params: { path: string[] } }
) {
  return forwardToLambda(request, 'POST', params.path);
}

export async function PUT(
  request: NextRequest,
  { params }: { params: { path: string[] } }
) {
  return forwardToLambda(request, 'PUT', params.path);
}

export async function DELETE(
  request: NextRequest,
  { params }: { params: { path: string[] } }
) {
  return forwardToLambda(request, 'DELETE', params.path);
}

export async function PATCH(
  request: NextRequest,
  { params }: { params: { path: string[] } }
) {
  return forwardToLambda(request, 'PATCH', params.path);
}
```

### 专用 API 路由示例
```typescript
// src/app/api/products/route.ts
import { NextRequest, NextResponse } from 'next/server';
import { verifyAuth } from '@/lib/auth';

export async function GET(request: NextRequest) {
  try {
    // 验证认证
    const user = await verifyAuth(request);
    if (!user) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 });
    }

    // 获取查询参数
    const { searchParams } = new URL(request.url);
    const page = searchParams.get('page') || '1';
    const pageSize = searchParams.get('pageSize') || '10';
    const search = searchParams.get('search') || '';
    const category = searchParams.get('category') || '';

    // 构建 Lambda 请求
    const lambdaUrl = `${process.env.AWS_LAMBDA_BASE_URL}/prod/products`;
    const params = new URLSearchParams({
      page,
      pageSize,
      ...(search && { search }),
      ...(category && { category }),
    });

    const response = await fetch(`${lambdaUrl}?${params}`, {
      headers: {
        'Authorization': `Bearer ${user.token}`,
        'Content-Type': 'application/json',
        'x-api-key': process.env.AWS_API_KEY || '',
      },
    });

    if (!response.ok) {
      throw new Error(`Lambda request failed: ${response.status}`);
    }

    const data = await response.json();
    return NextResponse.json(data);

  } catch (error) {
    console.error('Products API error:', error);
    return NextResponse.json(
      { error: 'Failed to fetch products' },
      { status: 500 }
    );
  }
}

export async function POST(request: NextRequest) {
  try {
    const user = await verifyAuth(request);
    if (!user) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 });
    }

    const body = await request.json();
    
    // 数据验证
    const validatedData = productSchema.parse(body);

    // 转发到 Lambda
    const lambdaUrl = `${process.env.AWS_LAMBDA_BASE_URL}/prod/products`;
    const response = await fetch(lambdaUrl, {
      method: 'POST',
      headers: {
        'Authorization': `Bearer ${user.token}`,
        'Content-Type': 'application/json',
        'x-api-key': process.env.AWS_API_KEY || '',
      },
      body: JSON.stringify({
        ...validatedData,
        createdBy: user.id,
      }),
    });

    if (!response.ok) {
      throw new Error(`Lambda request failed: ${response.status}`);
    }

    const data = await response.json();
    return NextResponse.json(data, { status: 201 });

  } catch (error) {
    if (error instanceof z.ZodError) {
      return NextResponse.json(
        { error: 'Validation failed', details: error.errors },
        { status: 400 }
      );
    }

    console.error('Create product error:', error);
    return NextResponse.json(
      { error: 'Failed to create product' },
      { status: 500 }
    );
  }
}
```

### 错误处理和重试机制
```typescript
// src/lib/lambda-client.ts
interface LambdaRequestConfig {
  url: string;
  method: string;
  headers?: Record<string, string>;
  body?: any;
  retries?: number;
  timeout?: number;
}

export class LambdaClient {
  private baseUrl: string;
  private defaultHeaders: Record<string, string>;

  constructor() {
    this.baseUrl = process.env.AWS_LAMBDA_BASE_URL || '';
    this.defaultHeaders = {
      'Content-Type': 'application/json',
      'x-api-key': process.env.AWS_API_KEY || '',
    };
  }

  async request<T>(config: LambdaRequestConfig): Promise<T> {
    const {
      url,
      method,
      headers = {},
      body,
      retries = 3,
      timeout = 30000,
    } = config;

    const fullUrl = url.startsWith('http') ? url : `${this.baseUrl}${url}`;
    const requestHeaders = { ...this.defaultHeaders, ...headers };

    for (let attempt = 0; attempt <= retries; attempt++) {
      try {
        const controller = new AbortController();
        const timeoutId = setTimeout(() => controller.abort(), timeout);

        const response = await fetch(fullUrl, {
          method,
          headers: requestHeaders,
          body: body ? JSON.stringify(body) : undefined,
          signal: controller.signal,
        });

        clearTimeout(timeoutId);

        if (!response.ok) {
          // 对于 4xx 错误，不进行重试
          if (response.status >= 400 && response.status < 500) {
            const errorData = await response.json().catch(() => ({}));
            throw new Error(`Client error: ${response.status} - ${JSON.stringify(errorData)}`);
          }
          
          // 5xx 错误进行重试
          throw new Error(`Server error: ${response.status}`);
        }

        return await response.json();

      } catch (error) {
        const isLastAttempt = attempt === retries;
        
        if (isLastAttempt) {
          throw error;
        }

        // 指数退避
        const delay = Math.pow(2, attempt) * 1000;
        await new Promise(resolve => setTimeout(resolve, delay));
      }
    }

    throw new Error('Max retries exceeded');
  }

  // 便捷方法
  get<T>(url: string, headers?: Record<string, string>): Promise<T> {
    return this.request<T>({ url, method: 'GET', headers });
  }

  post<T>(url: string, body: any, headers?: Record<string, string>): Promise<T> {
    return this.request<T>({ url, method: 'POST', body, headers });
  }

  put<T>(url: string, body: any, headers?: Record<string, string>): Promise<T> {
    return this.request<T>({ url, method: 'PUT', body, headers });
  }

  delete<T>(url: string, headers?: Record<string, string>): Promise<T> {
    return this.request<T>({ url, method: 'DELETE', headers });
  }
}

export const lambdaClient = new LambdaClient();
```

### 环境配置
```bash
# .env.local
AWS_LAMBDA_BASE_URL=https://your-api-gateway-url.amazonaws.com
AWS_API_KEY=your-api-key
AWS_REGION=us-east-1
NEXT_PUBLIC_API_BASE_URL=http://localhost:3000/api
```

## 开发工作流

### 1. 创建新功能模块
```bash
# 创建功能目录
mkdir -p src/app/\(main\)/dashboard/orders/_components

# 创建基础文件
touch src/app/\(main\)/dashboard/orders/page.tsx
touch src/app/\(main\)/dashboard/orders/_components/schema.ts
touch src/app/\(main\)/dashboard/orders/_components/columns.tsx
touch src/app/\(main\)/dashboard/orders/_components/filters.tsx
```

### 2. 添加 API 路由
```bash
# 创建 API 路由
mkdir -p src/app/api/orders
touch src/app/api/orders/route.ts
touch src/app/api/orders/[id]/route.ts
```

### 3. 更新菜单配置
```typescript
// 在 sidebar-items.ts 中添加新菜单项
{
  title: "订单管理",
  url: "/dashboard/orders",
  icon: ShoppingCart,
}
```

### 4. 开发和测试
```bash
# 启动开发服务器
npm run dev

# 在另一个终端运行类型检查
npm run type-check

# 运行代码检查
npm run lint
```

## 最佳实践

### 1. 代码组织
- ✅ 使用 Colocation 模式，相关代码放在一起
- ✅ 组件名称使用 PascalCase
- ✅ 文件名使用 kebab-case
- ✅ 保持组件单一职责

### 2. 类型安全
- ✅ 使用 Zod 进行数据验证
- ✅ 为所有组件定义 TypeScript 接口
- ✅ 避免使用 `any` 类型

### 3. 性能优化
- ✅ 使用 React.memo 优化组件渲染
- ✅ 实现分页和虚拟滚动
- ✅ 使用 Next.js 图片优化
- ✅ 合理使用 Server Components

### 4. 用户体验
- ✅ 提供加载状态反馈
- ✅ 实现错误边界处理
- ✅ 支持键盘导航
- ✅ 确保移动端适配

### 5. 安全性
- ✅ 验证所有用户输入
- ✅ 使用 HTTPS 传输敏感数据
- ✅ 实现适当的认证和授权
- ✅ 防止 XSS 和 CSRF 攻击

## 故障排除

### 常见问题

1. **中间件不生效**
   - 检查文件名是否为 `middleware.ts`
   - 确认文件位于 `src/` 目录下
   - 检查 `config.matcher` 配置

2. **样式不生效**
   - 确认已导入 Tailwind CSS
   - 检查组件是否正确使用 CSS 类名
   - 查看浏览器开发者工具中的样式

3. **API 请求失败**
   - 检查环境变量配置
   - 确认 Lambda 端点可访问
   - 查看网络请求的错误信息

4. **数据表格分页问题**
   - 确认服务端返回正确的分页数据
   - 检查 `useDataTableInstance` 配置
   - 验证 URL 参数解析逻辑

### 调试技巧

1. **使用 React DevTools** 查看组件状态
2. **使用 Network 面板** 调试 API 请求
3. **查看 Next.js 开发者工具** 分析路由和渲染
4. **使用 console.log** 输出关键变量值

---

这份指南涵盖了 Trade Manage Frontend 项目的核心开发模式。如果你有具体的开发需求，可以参考相应章节的示例代码和最佳实践。