/* 
label: Soft Pop
value: soft-pop 
*/

:root[data-theme-preset="soft-pop"] {
    --radius: 1rem;
    --card: oklch(1.0000 0 0);
    --card-foreground: oklch(0 0 0);
    --popover: oklch(1.0000 0 0);
    --popover-foreground: oklch(0 0 0);
    --primary: oklch(0.5106 0.2301 276.9656);
    --primary-foreground: oklch(1.0000 0 0);
    --secondary: oklch(0.7038 0.1230 182.5025);
    --secondary-foreground: oklch(1.0000 0 0);
    --muted: oklch(0.9551 0 0);
    --muted-foreground: oklch(0.3211 0 0);
    --accent: oklch(0.7686 0.1647 70.0804);
    --accent-foreground: oklch(0 0 0);
    --destructive: oklch(0.6368 0.2078 25.3313);
    --border: oklch(0 0 0);
    --input: oklch(0.5555 0 0);
    --ring: oklch(0.7853 0.1041 274.7134);
    --chart-1: oklch(0.5106 0.2301 276.9656);
    --chart-2: oklch(0.7038 0.1230 182.5025);
    --chart-3: oklch(0.7686 0.1647 70.0804);
    --chart-4: oklch(0.6559 0.2118 354.3084);
    --chart-5: oklch(0.7227 0.1920 149.5793);
    --sidebar: oklch(0.9789 0.0082 121.6272);
    --sidebar-foreground: oklch(0 0 0);
    --sidebar-primary: oklch(0.5106 0.2301 276.9656);
    --sidebar-primary-foreground: oklch(1.0000 0 0);
    --sidebar-accent: oklch(0.7686 0.1647 70.0804);
    --sidebar-accent-foreground: oklch(0 0 0);
    --sidebar-border: oklch(0 0 0);
    --sidebar-ring: oklch(0.7853 0.1041 274.7134);
    --background: oklch(0.9789 0.0082 121.6272);
    --foreground: oklch(0 0 0);
    --shadow-2xs: 0px 0px 0px 0px hsl(0 0% 10.1961% / 0.03);
    --shadow-xs: 0px 0px 0px 0px hsl(0 0% 10.1961% / 0.03);
    --shadow-sm: 0px 0px 0px 0px hsl(0 0% 10.1961% / 0.05), 0px 1px 2px -1px hsl(0 0% 10.1961% / 0.05);
    --shadow: 0px 0px 0px 0px hsl(0 0% 10.1961% / 0.05), 0px 1px 2px -1px hsl(0 0% 10.1961% / 0.05);
    --shadow-md: 0px 0px 0px 0px hsl(0 0% 10.1961% / 0.05), 0px 2px 4px -1px hsl(0 0% 10.1961% / 0.05);
    --shadow-lg: 0px 0px 0px 0px hsl(0 0% 10.1961% / 0.05), 0px 4px 6px -1px hsl(0 0% 10.1961% / 0.05);
    --shadow-xl: 0px 0px 0px 0px hsl(0 0% 10.1961% / 0.05), 0px 8px 10px -1px hsl(0 0% 10.1961% / 0.05);
    --shadow-2xl: 0px 0px 0px 0px hsl(0 0% 10.1961% / 0.13);
}

.dark:root[data-theme-preset="soft-pop"] {
    --background: oklch(0 0 0);
    --foreground: oklch(1.0000 0 0);
    --card: oklch(0.2455 0.0217 257.2823);
    --card-foreground: oklch(1.0000 0 0);
    --popover: oklch(0.2455 0.0217 257.2823);
    --popover-foreground: oklch(1.0000 0 0);
    --primary: oklch(0.6801 0.1583 276.9349);
    --primary-foreground: oklch(0 0 0);
    --secondary: oklch(0.7845 0.1325 181.9120);
    --secondary-foreground: oklch(0 0 0);
    --muted: oklch(0.3211 0 0);
    --muted-foreground: oklch(0.8452 0 0);
    --accent: oklch(0.8790 0.1534 91.6054);
    --accent-foreground: oklch(0 0 0);
    --destructive: oklch(0.7106 0.1661 22.2162);
    --border: oklch(0.4459 0 0);
    --input: oklch(1.0000 0 0);
    --ring: oklch(0.6801 0.1583 276.9349);
    --chart-1: oklch(0.6801 0.1583 276.9349);
    --chart-2: oklch(0.7845 0.1325 181.9120);
    --chart-3: oklch(0.8790 0.1534 91.6054);
    --chart-4: oklch(0.7253 0.1752 349.7607);
    --chart-5: oklch(0.8003 0.1821 151.7110);
    --sidebar: oklch(0 0 0);
    --sidebar-foreground: oklch(1.0000 0 0);
    --sidebar-primary: oklch(0.6801 0.1583 276.9349);
    --sidebar-primary-foreground: oklch(0 0 0);
    --sidebar-accent: oklch(0.8790 0.1534 91.6054);
    --sidebar-accent-foreground: oklch(0 0 0);
    --sidebar-border: oklch(1.0000 0 0);
    --sidebar-ring: oklch(0.6801 0.1583 276.9349);
    --shadow-2xs: 0px 0px 0px 0px hsl(0 0% 10.1961% / 0.03);
    --shadow-xs: 0px 0px 0px 0px hsl(0 0% 10.1961% / 0.03);
    --shadow-sm: 0px 0px 0px 0px hsl(0 0% 10.1961% / 0.05), 0px 1px 2px -1px hsl(0 0% 10.1961% / 0.05);
    --shadow: 0px 0px 0px 0px hsl(0 0% 10.1961% / 0.05), 0px 1px 2px -1px hsl(0 0% 10.1961% / 0.05);
    --shadow-md: 0px 0px 0px 0px hsl(0 0% 10.1961% / 0.05), 0px 2px 4px -1px hsl(0 0% 10.1961% / 0.05);
    --shadow-lg: 0px 0px 0px 0px hsl(0 0% 10.1961% / 0.05), 0px 4px 6px -1px hsl(0 0% 10.1961% / 0.05);
    --shadow-xl: 0px 0px 0px 0px hsl(0 0% 10.1961% / 0.05), 0px 8px 10px -1px hsl(0 0% 10.1961% / 0.05);
    --shadow-2xl: 0px 0px 0px 0px hsl(0 0% 10.1961% / 0.13);
}